# 密码设置功能需求文档

## 1. 需求概述

### 1.1 背景
当前系统仅支持手机验证码登录和Google OAuth登录，为了提升用户体验和提供更多登录选择，需要为有手机号的用户增加密码登录功能。


## 2. 功能需求

### 2.1 登录页面功能扩展（更新版）

#### 2.1.1 整体布局设计
**选项卡切换布局**
- 顶部：两个选项卡标题
  - 左侧："短信登录"（默认选中）
  - 右侧："密码登录"
- 内容区域：根据选中的选项卡显示对应内容
- 默认进入短信登录模式
- 保持现有毛玻璃效果和暗黑模式风格
- 保持现有按钮样式，不修改颜色

#### 2.1.2 短信登录区域（左侧选项卡，默认显示）
**界面元素**
- 手机号输入框：固定"+86"前缀，占位符"请输入手机号"
- 验证码输入框：标签"验证码"，占位符"请输入验证码"，内置"获取验证码"按钮
- 底部单按钮："登录/注册"（使用现有按钮样式）

**功能逻辑**
- 获取验证码：发送短信验证码到手机
- 登录/注册按钮：验证码验证成功后自动登录（新用户自动注册）
- 不区分新老用户，统一验证码登录流程
- 不支持国家代码选择，仅支持+86手机号

#### 2.1.3 密码登录区域（右侧选项卡，点击切换显示）
**界面元素**
- 账号输入框：标签"账号"，占位符"请输入账号"
- 密码输入框：标签"密码"，占位符"请输入密码"
- 密码输入框内置"忘记密码?"链接（右侧）
- 底部双按钮：
  - 左侧："注册"按钮（使用现有按钮样式）
  - 右侧："登录"按钮（使用现有按钮样式）

**功能逻辑**
- 注册按钮：跳转到密码注册页面
- 登录按钮：执行密码登录验证（账号+密码）
- 忘记密码链接：通过绑定手机号验证码重置密码

#### 2.1.4 交互逻辑
- 默认显示短信登录内容
- 点击"密码登录"选项卡切换到密码登录内容
- 点击"短信登录"选项卡切换回短信登录内容
- 选项卡有明显的选中/未选中状态

#### 2.1.5 密码注册功能（统一手机号验证方案）
**注册流程**
1. 手机号输入 + 发送并验证手机验证码
2. 账号设置（用户名）
3. 设置登录密码 + 确认密码
4. 完成注册并自动登录

**核心原则**
- 所有注册都必须绑定手机号
- 确保每个用户都有密码找回途径
- 注册完成后支持两种登录方式：
  - 手机号 + 验证码登录
  - 账号 + 密码登录

**密码设置要求**
- 长度：8-20位字符
- 复杂度：至少包含字母和数字
- 实时密码强度提示（弱/中/强）
- 密码可见性切换按钮

**短信注册功能（保留原有功能）**
- 手机号 + 验证码直接登录/注册
- 新用户自动创建账号
- 可后续在设置中添加密码

#### 2.1.6 忘记密码功能（统一手机号验证方案）
**忘记密码流程**
1. 用户在密码登录界面点击"忘记密码?"
2. 输入账号（用户名）
3. 系统自动查找该账号绑定的手机号
4. 向绑定手机号发送验证码
5. 验证码验证成功后进入密码重置页面
6. 设置新密码 + 确认密码
7. 重置成功，返回登录页面

**技术实现**
- 通过账号查询用户绑定的手机号
- 发送验证码到绑定手机号
- 包含密码强度实时提示
- 支持密码可见性切换

**安全保障**
- 由于所有用户注册时都必须绑定手机号，确保100%的用户都能找回密码
- 验证码有效期和重发限制
- 密码重置成功后强制重新登录

### 2.2 个人设置页面扩展

#### 2.2.1 显示逻辑
**核心规则：只有绑定手机号的用户才显示密码管理功能**

```
if (用户有手机号) {
    显示密码管理模块
} else {
    不显示密码管理模块
}
```

#### 2.2.2 密码管理模块
在"身份验证与账号绑定"部分添加：

**设置密码**（针对有手机号但未设置密码的用户）
- 验证手机号（发送验证码）
- 设置新密码
- 确认密码
- 完成设置

**修改密码**（针对已设置密码的用户）
- 输入原密码验证
- 设置新密码
- 确认新密码
- 完成修改

**密码状态显示**
- 密码设置状态：已设置/未设置
- 最后修改时间
- 密码强度评估

## 3. 界面设计规范

### 3.1 登录页面设计
**选项卡布局**
- 顶部选项卡：`短信登录` | `密码登录`
- 短信登录默认选中状态
- 选项卡切换时平滑过渡动画
- 保持现有毛玻璃容器和暗黑模式风格

**短信登录界面**
- 手机号输入框：固定"+86"前缀显示
- 验证码输入框：内置"获取验证码"按钮
- 单个"登录/注册"按钮，使用现有按钮样式

**密码登录界面**
- 账号输入框：标准输入框样式
- 密码输入框：内置"忘记密码?"链接
- 双按钮布局："注册" + "登录"，使用现有按钮样式

**注册界面（保留原有设计）**
- 复用现有登录页面的毛玻璃容器
- 保持相同的输入框样式和间距
- 密码输入框添加强度指示器
- 注册按钮使用现有样式

### 3.2 设置页面设计
**密码管理卡片**
```
┌─ 密码管理 ─────────────────────────┐
│ 登录密码                           │
│ ● 已设置 (2024年1月15日)           │
│ 密码强度: ●●●○○ 中等              │
│                                   │
│ [修改密码]  [密码安全提示]         │
└───────────────────────────────────┘
```

**设计要求**
- 与现有邮箱管理卡片保持一致的样式
- 支持暗黑模式和毛玻璃效果
- 按钮样式统一使用系统设计规范

## 4. 技术实现

### 4.1 前端实现
**新增页面组件**
- 选项卡切换组件（短信登录/密码登录）
- 短信登录组件（统一登录/注册逻辑）
- 密码登录组件（独立注册/登录按钮）
- 注册页面组件（保留原有）
- 密码设置/修改弹窗组件
- 密码强度指示器组件

**修改现有组件**
- 登录页面：重构为选项卡布局，默认短信登录
- 设置页面：添加密码管理模块
- 用户状态管理：添加密码相关状态

**主要代码修改点**
1. HTML结构：重构为选项卡布局，短信登录在左
2. CSS样式：调整选项卡样式，保持现有按钮样式
3. JavaScript逻辑：实现选项卡切换逻辑，默认选中短信登录
4. 交互流程：短信登录统一登录/注册流程

### 4.2 数据存储
**用户表扩展字段**
```sql
ALTER TABLE users ADD COLUMN username VARCHAR(50) UNIQUE NULL;     # 用户名（账号）
ALTER TABLE users ADD COLUMN phone VARCHAR(20) NOT NULL;          # 手机号（必填）
ALTER TABLE users ADD COLUMN password_hash VARCHAR(255) NULL;     # 密码哈希
ALTER TABLE users ADD COLUMN password_updated_at TIMESTAMP NULL;  # 密码更新时间
ALTER TABLE users ADD COLUMN login_preference ENUM('password', 'sms') DEFAULT 'sms';
ALTER TABLE users ADD COLUMN created_via ENUM('sms', 'password') NOT NULL; # 注册方式
```

**数据约束**
- 手机号必填且唯一
- 用户名可选但唯一（密码注册时必填）
- 每个用户都有手机号，确保密码找回可用

**密码安全**
- 使用bcrypt进行密码哈希
- 盐值长度：12位
- 密码不可逆加密存储

### 4.3 API接口
**新增接口**
```
POST /api/auth/sms/login         # 短信登录/注册（统一接口）
POST /api/auth/register/password # 密码注册（手机号+账号+密码）
POST /api/auth/login/password    # 密码登录（账号+密码）
POST /api/auth/password/reset    # 重置密码（通过账号查找手机号）
POST /api/auth/password/set      # 设置密码
POST /api/auth/password/change   # 修改密码
GET  /api/user/password/status   # 密码状态查询
GET  /api/user/phone/by-account  # 通过账号查询绑定手机号
```

**短信登录/注册统一逻辑**
- 验证码验证成功后，系统自动判断用户是否存在
- 存在用户：直接登录
- 新用户：自动创建账号并登录
- 返回统一的登录成功响应

**密码注册逻辑**
- 验证手机号和验证码
- 检查账号是否已存在
- 创建用户账号，同时绑定手机号和设置密码
- 返回注册成功并自动登录

**忘记密码逻辑**
- 通过账号查询绑定的手机号
- 向绑定手机号发送验证码
- 验证码验证成功后允许重置密码
- 确保所有用户都能找回密码

## 5. 安全策略

### 5.1 密码安全
- 密码长度：8-20位
- 复杂度要求：字母+数字组合
- 禁止使用常见弱密码
- 密码历史：不允许重复最近3次密码

### 5.2 登录安全
- 登录失败限制：5次失败锁定30分钟
- 异常登录检测：新设备登录提醒
- 会话管理：支持"记住我"功能

### 5.3 操作安全
- 敏感操作需要二次验证
- 密码修改需要验证原密码或手机验证码
- 所有密码相关操作记录日志

## 6. 用户体验

### 6.1 注册体验
- 实时表单验证和友好错误提示
- 密码强度实时反馈
- 注册成功后自动登录并跳转

### 6.2 登录体验
- 默认显示短信登录，简化用户选择
- 选项卡切换流畅，状态明确
- 记住上次登录的手机号
- 短信登录统一处理新老用户，无需区分
- 密码登录失败时提供找回密码入口

### 6.3 设置体验
- 密码状态清晰展示
- 操作确认和成功反馈
- 安全提示和帮助信息


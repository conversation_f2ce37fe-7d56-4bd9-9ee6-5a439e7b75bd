# 密码设置功能需求文档

## 1. 需求概述

### 1.1 背景
当前系统仅支持手机验证码登录和Google OAuth登录，为了提升用户体验和提供更多登录选择，需要为有手机号的用户增加密码登录功能。


## 2. 功能需求

### 2.1 登录页面功能扩展

#### 2.1.1 注册功能
**注册入口**
- 在登录页面添加"注册账号"链接
- 点击后切换到注册界面

**注册流程**
1. 手机号输入（复用现有组件）
2. 发送并验证手机验证码
3. 设置登录密码
4. 确认密码
5. 完成注册并自动登录

**密码设置要求**
- 长度：8-20位字符
- 复杂度：至少包含字母和数字
- 实时密码强度提示（弱/中/强）
- 密码可见性切换按钮

#### 2.1.2 登录方式切换
**支持的登录方式**
- 密码登录：手机号 + 密码
- 验证码登录：手机号 + 验证码（现有）
- Google登录（现有）

**界面设计**
- 添加"密码登录"/"验证码登录"切换按钮，每次只出现一种登录方式
- 保持现有毛玻璃效果和暗黑模式风格
- 动态切换输入框类型

#### 2.1.3 忘记密码功能
- 在密码登录界面添加"忘记密码？"链接
- 通过手机验证码重置密码流程

### 2.2 个人设置页面扩展

#### 2.2.1 显示逻辑
**核心规则：只有绑定手机号的用户才显示密码管理功能**

```
if (用户有手机号) {
    显示密码管理模块
} else {
    不显示密码管理模块
}
```

#### 2.2.2 密码管理模块
在"身份验证与账号绑定"部分添加：

**设置密码**（针对有手机号但未设置密码的用户）
- 验证手机号（发送验证码）
- 设置新密码
- 确认密码
- 完成设置

**修改密码**（针对已设置密码的用户）
- 输入原密码验证
- 设置新密码
- 确认新密码
- 完成修改

**密码状态显示**
- 密码设置状态：已设置/未设置
- 最后修改时间
- 密码强度评估

## 3. 界面设计规范

### 3.1 登录页面设计
**注册界面**
- 复用现有登录页面的毛玻璃容器
- 保持相同的输入框样式和间距
- 密码输入框添加强度指示器
- 注册按钮使用主色调

**登录方式切换**
- 在现有手机号输入框上方添加选项卡
- 选项卡样式：`密码登录` | `验证码登录`
- 切换时平滑过渡动画

### 3.2 设置页面设计
**密码管理卡片**
```
┌─ 密码管理 ─────────────────────────┐
│ 登录密码                           │
│ ● 已设置 (2024年1月15日)           │
│ 密码强度: ●●●○○ 中等              │
│                                   │
│ [修改密码]  [密码安全提示]         │
└───────────────────────────────────┘
```

**设计要求**
- 与现有邮箱管理卡片保持一致的样式
- 支持暗黑模式和毛玻璃效果
- 按钮样式统一使用系统设计规范

## 4. 技术实现

### 4.1 前端实现
**新增页面组件**
- 注册页面组件
- 密码设置/修改弹窗组件
- 密码强度指示器组件

**修改现有组件**
- 登录页面：添加登录方式切换
- 设置页面：添加密码管理模块
- 用户状态管理：添加密码相关状态

### 4.2 数据存储
**用户表扩展字段**
```sql
ALTER TABLE users ADD COLUMN password_hash VARCHAR(255) NULL;
ALTER TABLE users ADD COLUMN password_updated_at TIMESTAMP NULL;
ALTER TABLE users ADD COLUMN login_preference ENUM('password', 'sms') DEFAULT 'sms';
```

**密码安全**
- 使用bcrypt进行密码哈希
- 盐值长度：12位
- 密码不可逆加密存储

### 4.3 API接口
**新增接口**
```
POST /api/auth/register          # 手机号注册
POST /api/auth/login/password    # 密码登录
POST /api/auth/password/set      # 设置密码
POST /api/auth/password/change   # 修改密码
POST /api/auth/password/reset    # 重置密码
GET  /api/user/password/status   # 密码状态查询
```

## 5. 安全策略

### 5.1 密码安全
- 密码长度：8-20位
- 复杂度要求：字母+数字组合
- 禁止使用常见弱密码
- 密码历史：不允许重复最近3次密码

### 5.2 登录安全
- 登录失败限制：5次失败锁定30分钟
- 异常登录检测：新设备登录提醒
- 会话管理：支持"记住我"功能

### 5.3 操作安全
- 敏感操作需要二次验证
- 密码修改需要验证原密码或手机验证码
- 所有密码相关操作记录日志

## 6. 用户体验

### 6.1 注册体验
- 实时表单验证和友好错误提示
- 密码强度实时反馈
- 注册成功后自动登录并跳转

### 6.2 登录体验
- 记住上次登录的手机号
- 智能推荐登录方式
- 登录失败时提供找回密码入口

### 6.3 设置体验
- 密码状态清晰展示
- 操作确认和成功反馈
- 安全提示和帮助信息


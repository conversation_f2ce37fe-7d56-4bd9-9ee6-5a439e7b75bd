// ===============================================
// == 登录、注册、密码重置页面专属脚本 ==
// ===============================================

document.addEventListener('DOMContentLoaded', () => {
    console.log('🔒 Auth script loaded');

    // 尝试获取所有需要的元素
    const loginPage = document.getElementById('login-page');
    const registerPage = document.getElementById('register-page');
    const forgotPasswordPage = document.getElementById('forgot-password-page');
    
    // 由于这是登录页专属脚本，我们期望 loginPage 存在
    if (loginPage) {
        setupLoginPage();
    } else if (registerPage) {
        setupRegisterPage();
    } else if (forgotPasswordPage) {
        setupForgotPasswordPage();
    } else {
        console.error('无法找到登录、注册或忘记密码页面容器。');
    }
});


/**
 * 设置登录和注册页面的核心功能
 */
function setupLoginPage() {
    console.log('🚀 Initializing login page...');

    // 页面切换逻辑
    const registerLink = document.getElementById('register-link');
    const backToLoginLink = document.getElementById('back-to-login-link');
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    const backToLoginFromReset = document.getElementById('back-to-login-from-reset');

    if (registerLink) {
        registerLink.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('register-page');
        });
    }

    if (backToLoginLink) {
        backToLoginLink.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('login-page');
        });
    }

    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('forgot-password-page');
        });
    }

    if (backToLoginFromReset) {
        backToLoginFromReset.addEventListener('click', (e) => {
            e.preventDefault();
            showPage('login-page');
        });
    }

    // 初始化登录表单功能
    initLoginForm();
    // 初始化注册表单功能
    initRegisterForm();
    // 初始化忘记密码表单功能
    initForgotPasswordForm();
    // 初始化登录方式切换
    initLoginModeTabs();
    
    // 默认显示登录页面
    showPage('login-page');
}


/**
 * 显示指定的页面
 * @param {string} pageId 
 */
function showPage(pageId) {
    const pages = ['login-page', 'register-page', 'forgot-password-page'];
    
    pages.forEach(id => {
        const page = document.getElementById(id);
        if (page) {
            if (id === pageId) {
                page.style.display = 'block';
                page.classList.remove('fade-out');
            } else {
                page.classList.add('fade-out');
                setTimeout(() => {
                    page.style.display = 'none';
                }, 300);
            }
        }
    });
}


/**
 * 初始化登录表单
 */
function initLoginForm() {
    const loginBtn = document.getElementById('login-btn-new');
    const googleLoginBtn = document.getElementById('google-login-btn-new');
    const modeSwitchBtn = document.getElementById('login-mode-switch-btn');
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    const sendCodeBtn = document.getElementById('send-code-btn-new');
    const phoneInput = document.getElementById('phone-input-new');
    const verificationInput = document.getElementById('verification-input-new');
    const passwordInput = document.getElementById('password-input-new');
    
    initPasswordToggle('password-toggle-btn', 'password-input-new');
    
    if(loginBtn) {
        loginBtn.addEventListener('click', () => handleLogin());
    }

    if(googleLoginBtn) {
        googleLoginBtn.addEventListener('click', () => {
            showLoginMessage('正在跳转到 Google 登录...', 'info');
            // 模拟跳转
            setTimeout(() => {
                alert('与Google OAuth集成。');
            }, 1000);
        });
    }
    
    if(modeSwitchBtn) {
        modeSwitchBtn.addEventListener('click', () => switchLoginMode());
    }

    if(forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', (e) => {
            e.preventDefault();
            // 在当前页面流程中处理忘记密码，或跳转到单独页面
            alert('忘记密码流程启动。');
        });
    }

    if(sendCodeBtn) {
        sendCodeBtn.addEventListener('click', () => {
            handleSendVerificationCode(phoneInput.value, sendCodeBtn, 'login');
        });
    }
}

/**
 * 初始化注册表单
 */
function initRegisterForm() {
    const registerBtn = document.getElementById('register-btn');
    const sendCodeBtn = document.getElementById('register-send-code-btn');
    const phoneInput = document.getElementById('register-phone-input');
    const passwordInput = document.getElementById('register-password-input');
    
    initPasswordToggle('register-password-toggle-btn', 'register-password-input');
    initPasswordToggle('register-confirm-password-toggle-btn', 'register-confirm-password-input');

    if(registerBtn) {
        registerBtn.addEventListener('click', () => handleRegister());
    }

    if(sendCodeBtn) {
        sendCodeBtn.addEventListener('click', () => {
            handleSendVerificationCode(phoneInput.value, sendCodeBtn, 'register');
        });
    }

    if(passwordInput) {
        passwordInput.addEventListener('input', () => {
             checkPasswordStrength(passwordInput.value, 'register');
        });
    }
}

/**
 * 处理登录逻辑
 */
function handleLogin() {
    const phoneInput = document.getElementById('phone-input-new');
    const verificationInput = document.getElementById('verification-input-new');
    const passwordInput = document.getElementById('password-input-new');
    const smsTab = document.getElementById('sms-login-tab');

    const phone = phoneInput.value;
    const isPasswordMode = !smsTab.classList.contains('active');

    if (isPasswordMode) {
        // 密码登录
        const password = passwordInput.value;
        if (!phone || !password) {
            showLoginMessage('手机号和密码不能为空', 'error');
            return;
        }
        showLoginMessage(`正在使用密码为 ${phone} 登录...`, 'info');
        // 模拟API调用
        setTimeout(() => {
            localStorage.setItem('userLoggedIn', 'true');
            window.location.href = '../index.html';
        }, 1000);
    } else {
        // 验证码登录
        const code = verificationInput.value;
        if (!phone || !code) {
            showLoginMessage('手机号和验证码不能为空', 'error');
            return;
        }
        showLoginMessage(`正在使用验证码 ${code} 为 ${phone} 登录...`, 'info');
        // 模拟API调用
        setTimeout(() => {
            localStorage.setItem('userLoggedIn', 'true');
            window.location.href = '../index.html';
        }, 1000);
    }
}

/**
 * 处理注册逻辑
 */
function handleRegister() {
    const phone = document.getElementById('register-phone-input').value;
    const code = document.getElementById('register-verification-input').value;
    const password = document.getElementById('register-password-input').value;
    const confirmPassword = document.getElementById('register-confirm-password-input').value;

    if (!phone || !code || !password || !confirmPassword) {
        showRegisterMessage("请填写所有字段", 'error');
        return;
    }

    if (!isValidPassword(password)) {
        showRegisterMessage("密码必须为8-20位，包含字母和数字", 'error');
        return;
    }

    if (password !== confirmPassword) {
        showRegisterMessage("两次输入的密码不一致", 'error');
        return;
    }

    showRegisterMessage(`正在为 ${phone} 创建账号...`, 'info');
    // 模拟API调用
    setTimeout(() => {
        showRegisterMessage('注册成功！正在为您登录...', 'success');
        localStorage.setItem('userLoggedIn', 'true');
        setTimeout(() => {
            window.location.href = '../index.html';
        }, 1500);
    }, 1000);
}

/**
 * 切换登录模式 (验证码/密码)
 */
function switchLoginMode() {
    const verificationWrapper = document.getElementById('verification-wrapper');
    const passwordWrapper = document.getElementById('password-wrapper');
    const forgotPasswordWrapper = document.getElementById('forgot-password-wrapper');
    const switchBtn = document.getElementById('login-mode-switch-btn');
    const switchBtnText = switchBtn.querySelector('span');
    const switchBtnIcon = switchBtn.querySelector('i');

    if (passwordWrapper.style.display === 'none') {
        // 切换到密码登录
        verificationWrapper.style.display = 'none';
        passwordWrapper.style.display = 'flex';
        forgotPasswordWrapper.style.display = 'flex';
        switchBtnText.textContent = '验证码登录';
        switchBtnIcon.className = 'fas fa-shield-alt';
    } else {
        // 切换到验证码登录
        verificationWrapper.style.display = 'flex';
        passwordWrapper.style.display = 'none';
        forgotPasswordWrapper.style.display = 'none';
        switchBtnText.textContent = '密码登录';
        switchBtnIcon.className = 'fas fa-lock';
    }
}

/**
 * 初始化密码可见性切换按钮
 * @param {string} btnId 
 * @param {string} inputId 
 */
function initPasswordToggle(btnId, inputId) {
    const toggleBtn = document.getElementById(btnId);
    const passwordInput = document.getElementById(inputId);

    if (toggleBtn && passwordInput) {
        toggleBtn.addEventListener('click', () => {
            togglePasswordVisibility(passwordInput, toggleBtn);
        });
    }
}

/**
 * 切换密码的可见性
 * @param {HTMLInputElement} input 
 * @param {HTMLButtonElement} button 
 */
function togglePasswordVisibility(input, button) {
    const icon = button.querySelector('i');
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'ri-eye-line';
    } else {
        input.type = 'password';
        icon.className = 'ri-eye-off-line';
    }
}

/**
 * 检查密码强度
 * @param {string} password 
 * @param {string} prefix 'register' or 'reset'
 */
function checkPasswordStrength(password, prefix = 'register') {
    const strength = getPasswordStrength(password);
    const strengthBar = document.getElementById(`${prefix}-password-strength-fill`);
    const strengthText = document.getElementById(`${prefix}-password-strength-text`);

    if (!strengthBar || !strengthText) return;

    strengthBar.className = `password-strength-fill ${strength.level}`;
    strengthText.textContent = `密码强度：${strength.text}`;
    strengthText.className = `password-strength-text ${strength.level}`;
}

/**
 * 获取密码强度
 * @param {string} password 
 * @returns {{level: string, text: string}}
 */
function getPasswordStrength(password) {
    let score = 0;
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[^a-zA-Z0-9]/.test(password)) score++;

    if (score < 3) return { level: 'weak', text: '弱' };
    if (score < 4) return { level: 'medium', text: '中等' };
    return { level: 'strong', text: '强' };
}

/**
 * 处理发送验证码
 * @param {string} phone
 * @param {HTMLButtonElement} button
 * @param {string} prefix
 */
function handleSendVerificationCode(phone, button, prefix) {
    if (!isValidPhoneNumber(phone)) {
        if (prefix === 'login') showLoginMessage('请输入有效的手机号', 'error');
        else if (prefix === 'register') showRegisterMessage('请输入有效的手机号', 'error');
        else if (prefix === 'reset') showResetMessage('请输入有效的手机号', 'error');
        return;
    }

    if (prefix === 'login') showLoginMessage(`验证码已发送至 ${phone}`, 'success');
    else if (prefix === 'register') showRegisterMessage(`验证码已发送至 ${phone}`, 'success');
    else if (prefix === 'reset') showResetMessage(`验证码已发送至 ${phone}`, 'success');

    startCountdown(button);
}

/**
 * 验证手机号格式
 * @param {string} phone 
 * @returns {boolean}
 */
function isValidPhoneNumber(phone) {
    // 简单验证，实际应用中应使用更复杂的正则表达式
    return /^1[3-9]\d{9}$/.test(phone);
}


/**
 * 开始发送验证码按钮的倒计时
 * @param {HTMLButtonElement} button 
 * @param {number} seconds 
 */
function startCountdown(button, seconds = 60) {
    button.disabled = true;
    let timer = seconds;
    button.textContent = `${timer}s 后重发`;

    const interval = setInterval(() => {
        timer--;
        if (timer > 0) {
            button.textContent = `${timer}s 后重发`;
        } else {
            clearInterval(interval);
            button.disabled = false;
            button.textContent = '发送验证码';
        }
    }, 1000);
}


/**
 * 在登录页显示消息
 * @param {string} message 
 * @param {'info' | 'success' | 'error'} type 
 */
function showLoginMessage(message, type = 'info') {
    const container = document.getElementById('login-message-container');
    if (!container) return;
    
    const messageElement = document.createElement('div');
    messageElement.className = `login-message ${type}`;
    messageElement.textContent = message;
    
    container.innerHTML = '';
    container.appendChild(messageElement);

    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => messageElement.remove(), 500);
    }, 3000);
}

/**
 * 在注册页显示消息
 * @param {string} message 
 * @param {'info' | 'success' | 'error'} type 
 */
function showRegisterMessage(message, type = 'info') {
    const container = document.getElementById('register-message-container');
     if (!container) {
        // Fallback to login message container if not on register page
        showLoginMessage(message, type);
        return;
    }
    
    const messageElement = document.createElement('div');
    messageElement.className = `login-message ${type}`;
    messageElement.textContent = message;
    
    container.innerHTML = '';
    container.appendChild(messageElement);

    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => messageElement.remove(), 500);
    }, 3000);
}

/**
 * 初始化登录方式切换选项卡
 */
function initLoginModeTabs() {
    const smsTab = document.getElementById('sms-login-tab');
    const passwordTab = document.getElementById('password-login-tab');
    const verificationWrapper = document.getElementById('verification-wrapper');
    const passwordWrapper = document.getElementById('password-wrapper');
    const forgotPasswordWrapper = document.getElementById('forgot-password-wrapper');

    if (smsTab && passwordTab) {
        smsTab.addEventListener('click', () => {
            switchLoginMode('sms');
        });

        passwordTab.addEventListener('click', () => {
            switchLoginMode('password');
        });
    }

    function switchLoginMode(mode) {
        // 更新选项卡状态
        smsTab.classList.toggle('active', mode === 'sms');
        passwordTab.classList.toggle('active', mode === 'password');

        // 切换输入框显示
        if (mode === 'sms') {
            verificationWrapper.style.display = 'flex';
            passwordWrapper.style.display = 'none';
            forgotPasswordWrapper.style.display = 'none';
        } else {
            verificationWrapper.style.display = 'none';
            passwordWrapper.style.display = 'flex';
            forgotPasswordWrapper.style.display = 'flex';
        }
    }
}

/**
 * 初始化忘记密码表单
 */
function initForgotPasswordForm() {
    const resetBtn = document.getElementById('reset-password-btn');
    const sendCodeBtn = document.getElementById('reset-send-code-btn');
    const phoneInput = document.getElementById('reset-phone-input');
    const passwordInput = document.getElementById('reset-password-input');
    
    initPasswordToggle('reset-password-toggle-btn', 'reset-password-input');
    initPasswordToggle('reset-confirm-password-toggle-btn', 'reset-confirm-password-input');

    if (resetBtn) {
        resetBtn.addEventListener('click', () => handlePasswordReset());
    }

    if (sendCodeBtn) {
        sendCodeBtn.addEventListener('click', () => {
            handleSendVerificationCode(phoneInput.value, sendCodeBtn, 'reset');
        });
    }

    if (passwordInput) {
        passwordInput.addEventListener('input', () => {
            checkPasswordStrength(passwordInput.value, 'reset');
        });
    }
}

/**
 * 处理密码重置逻辑
 */
function handlePasswordReset() {
    const phone = document.getElementById('reset-phone-input').value;
    const code = document.getElementById('reset-verification-input').value;
    const password = document.getElementById('reset-password-input').value;
    const confirmPassword = document.getElementById('reset-confirm-password-input').value;

    if (!phone || !code || !password || !confirmPassword) {
        showResetMessage("请填写所有字段", 'error');
        return;
    }

    if (!isValidPassword(password)) {
        showResetMessage("密码必须为8-20位，包含字母和数字", 'error');
        return;
    }

    if (password !== confirmPassword) {
        showResetMessage("两次输入的密码不一致", 'error');
        return;
    }

    showResetMessage(`正在重置 ${phone} 的密码...`, 'info');
    // 模拟API调用
    setTimeout(() => {
        showResetMessage('密码重置成功！正在跳转到登录页面...', 'success');
        setTimeout(() => {
            showPage('login-page');
        }, 1500);
    }, 1000);
}

/**
 * 验证密码格式
 * @param {string} password 
 * @returns {boolean}
 */
function isValidPassword(password) {
    // 8-20位，至少包含字母和数字
    if (password.length < 8 || password.length > 20) return false;
    if (!/[a-zA-Z]/.test(password)) return false;
    if (!/\d/.test(password)) return false;
    return true;
}

/**
 * 在重置密码页显示消息
 * @param {string} message 
 * @param {'info' | 'success' | 'error'} type 
 */
function showResetMessage(message, type = 'info') {
    const container = document.getElementById('reset-message-container');
    if (!container) {
        showLoginMessage(message, type);
        return;
    }
    
    const messageElement = document.createElement('div');
    messageElement.className = `login-message ${type}`;
    messageElement.textContent = message;
    
    container.innerHTML = '';
    container.appendChild(messageElement);

    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => messageElement.remove(), 500);
    }, 3000);
}

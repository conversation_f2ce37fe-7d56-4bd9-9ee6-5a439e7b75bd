@import url('../../style.css'); /* 导入基础和主题变量 */

body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: var(--background-color);
    font-family: var(--font-family);
    color: var(--text-primary);
}

.email-config-card {
    background-color: var(--surface-color);
    padding: 32px;
    border-radius: var(--radius-lg, 12px);
    box-shadow: var(--shadow-elevation-3, 0 4px 12px var(--black-alpha-10));
    width: 100%;
    max-width: 500px;
    border: 1px solid var(--border-color);
}

.email-config-card h2 {
    font-size: 24px;
    color: var(--text-primary);
    margin-top: 0;
    margin-bottom: 16px;
    text-align: center;
}

.email-config-card > p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    text-align: center;
}

.email-provider {
    border: 1px solid var(--border-light);
    padding: 12px 16px;
    border-radius: var(--radius-base, 6px);
    margin-bottom: 24px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--animation-duration, 0.2s) var(--animation-easing, ease-in-out);
}

.email-provider:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

.guidance {
    background-color: var(--background-alt);
    padding: 16px;
    border-radius: var(--radius-base, 6px);
    margin-bottom: 24px;
    font-size: 14px;
}

.guidance p {
    margin-top: 0;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-weight: bold;
}

.guidance ol {
    padding-left: 20px;
    margin-bottom: 12px;
    color: var(--text-tertiary);
}

.guidance li {
    margin-bottom: 6px;
}

.auth-code-link {
    color: var(--link);
    text-decoration: none;
    font-weight: 500;
}

.auth-code-link:hover {
    color: var(--link-hover);
    text-decoration: underline;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-base, 6px);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 16px;
    transition: all var(--animation-duration, 0.2s) var(--animation-easing, ease-in-out);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-15);
}

.form-group input::placeholder {
    color: var(--text-tertiary);
}


.auto-config {
    margin-top: 24px;
    margin-bottom: 24px;
    border-top: 1px solid var(--border-light);
    padding-top: 16px;
}

.auto-config p {
    font-weight: bold;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.config-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    font-size: 14px;
}

.config-item span:first-child {
    color: var(--text-tertiary);
}

.config-item span:last-child {
    color: var(--text-primary);
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 16px;
    margin-top: 24px;
}

.form-actions button {
    flex-grow: 1;
    padding: 12px;
    border-radius: var(--radius-base, 6px);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--animation-duration, 0.2s) var(--animation-easing, ease-in-out);
    border: 1px solid transparent;
}

.btn-test {
    background-color: var(--surface-elevated);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-test:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

.btn-save {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

.btn-save:hover {
    background-color: var(--primary-hover);
    box-shadow: 0 2px 8px var(--primary-alpha-20);
} 